<template>
  <div class="test-container">
    <div class="test-header">
      <h1>拖拽功能测试</h1>
      <p>测试D3图形组件的拖拽功能是否正常工作</p>
      
      <div class="test-controls">
        <el-button @click="loadTestData" type="primary">加载测试数据</el-button>
        <el-button @click="switchToForce" type="success">切换到力导向布局</el-button>
        <el-button @click="switchToRadial" type="info">切换到同心圆布局</el-button>
        <el-text>当前布局: {{ currentLayout }}</el-text>
      </div>
      
      <div class="test-instructions">
        <el-alert
          title="拖拽测试说明"
          type="info"
          :closable="false"
        >
          <ul>
            <li>在<strong>力导向布局</strong>中：拖拽节点时会实时更新位置，释放后节点会继续受力影响</li>
            <li>在<strong>同心圆布局</strong>中：拖拽节点时会实时更新位置，释放后节点保持在拖拽位置</li>
            <li>拖拽时连接的边会实时跟随移动</li>
            <li>拖拽时鼠标指针会变为抓取状态</li>
          </ul>
        </el-alert>
      </div>
    </div>
    
    <div class="test-content">
      <D3Graph 
        ref="graphRef"
        :data="graphData" 
        :attrs="[]"
        v-model:panel-visible="false"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import D3Graph from '@/components/graph/d3-graph.vue';

const graphRef = ref(null);
const currentLayout = ref('radial');

// 测试数据
const graphData = ref({
  nodes: [],
  links: []
});

// 当前布局显示名称
const currentLayoutName = computed(() => {
  const names = {
    'radial': '同心圆布局',
    'force': '力导向布局',
    'treeTB': '垂直树布局',
    'treeLR': '水平树布局'
  };
  return names[currentLayout.value] || currentLayout.value;
});

// 加载测试数据
function loadTestData() {
  graphData.value = {
    nodes: [
      {
        id: "center",
        nodeName: "中心节点",
        labels: ["unit"],
        attrs: [{ code: "name", value: "中心节点" }]
      },
      {
        id: "node1",
        nodeName: "节点1",
        labels: ["person"],
        attrs: [{ code: "name", value: "节点1" }]
      },
      {
        id: "node2",
        nodeName: "节点2",
        labels: ["person"],
        attrs: [{ code: "name", value: "节点2" }]
      },
      {
        id: "node3",
        nodeName: "节点3",
        labels: ["train"],
        attrs: [{ code: "name", value: "节点3" }]
      },
      {
        id: "node4",
        nodeName: "节点4",
        labels: ["other"],
        attrs: [{ code: "name", value: "节点4" }]
      },
      {
        id: "node5",
        nodeName: "节点5",
        labels: ["unit"],
        attrs: [{ code: "name", value: "节点5" }]
      }
    ],
    links: [
      { source: "center", target: "node1", name: "连接1" },
      { source: "center", target: "node2", name: "连接2" },
      { source: "center", target: "node3", name: "连接3" },
      { source: "node1", target: "node4", name: "连接4" },
      { source: "node2", target: "node5", name: "连接5" },
      { source: "node3", target: "node4", name: "连接6" },
      { source: "node4", target: "node5", name: "连接7" }
    ]
  };
}

// 切换到力导向布局
function switchToForce() {
  currentLayout.value = 'force';
  // 通过模拟点击工具栏按钮来切换布局
  setTimeout(() => {
    const forceButton = document.querySelector('[aria-label="力导向布局"]');
    if (forceButton) {
      forceButton.click();
    }
  }, 100);
}

// 切换到同心圆布局
function switchToRadial() {
  currentLayout.value = 'radial';
  setTimeout(() => {
    const radialButton = document.querySelector('[aria-label="生态图布局（同心圆）"]');
    if (radialButton) {
      radialButton.click();
    }
  }, 100);
}

// 初始化
loadTestData();
</script>

<style scoped lang="scss">
.test-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.test-header {
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  
  h1 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 24px;
  }
  
  p {
    margin: 0 0 16px 0;
    color: #606266;
    font-size: 14px;
  }
}

.test-controls {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.test-instructions {
  margin-top: 16px;
  
  ul {
    margin: 8px 0 0 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 4px;
      line-height: 1.5;
      
      strong {
        color: #409eff;
      }
    }
  }
}

.test-content {
  flex: 1;
  position: relative;
  min-height: 0;
}

@media (max-width: 768px) {
  .test-header {
    padding: 16px;
    
    h1 {
      font-size: 20px;
    }
  }
  
  .test-controls {
    flex-direction: column;
    align-items: stretch;
    
    .el-button {
      width: 100%;
    }
  }
}
</style>
