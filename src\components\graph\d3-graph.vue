<template>
  <div ref="wrapEl" class="wrap" :class="{ 'drag-mode': isDragging }">
    <div ref="containerEl" class="graph"></div>

    <!-- 工具条：右上角横向图标 -->
    <div class="tooldock">
      <div class="dock">
        <!-- 回到中心 -->
        <el-tooltip content="回到中心点" placement="bottom" :show-after="300">
          <button class="dock-btn" @click="handleToCenter" aria-label="回到中心点">
            <el-icon><Aim /></el-icon>
          </button>
        </el-tooltip>

        <span class="dock-sep"></span>

        <!-- 垂直布局 -->
        <el-tooltip content="垂直布局" placement="bottom" :show-after="300">
          <button
            class="dock-btn"
            :class="{ active: isActive('treeTB') }"
            @click="handleFab('treeTB')"
            aria-label="垂直布局"
          >
            <el-icon><Connection /></el-icon>
          </button>
        </el-tooltip>

        <!-- 生态树布局 -->
        <el-tooltip content="生态树布局" placement="bottom" :show-after="300">
          <button
            class="dock-btn"
            :class="{ active: isActive('treeLR') }"
            @click="handleFab('treeLR')"
            aria-label="生态树布局"
          >
            <el-icon><Rank /></el-icon>
          </button>
        </el-tooltip>

        <!-- 同心圆 -->
        <el-tooltip content="生态图布局（同心圆）" placement="bottom" :show-after="300">
          <button
            class="dock-btn"
            :class="{ active: isActive('radial') }"
            @click="handleFab('radial')"
            aria-label="生态图布局（同心圆）"
          >
            <el-icon><PieChart /></el-icon>
          </button>
        </el-tooltip>

        <span class="dock-sep"></span>

        <!-- 全屏切换 -->
        <el-tooltip :content="isFullscreen ? '退出全屏' : '进入全屏'" placement="bottom" :show-after="300">
          <button class="dock-btn" @click.stop="toggleFullscreen" aria-label="全屏切换">
            <el-icon><FullScreen /></el-icon>
          </button>
        </el-tooltip>
      </div>
    </div>

    <!-- 右键信息面板 -->
    <div v-if="panelVisible" class="info-panel" @contextmenu.prevent>
      <el-card>
        <div class="panel-hd">
          <div class="title">{{ panel.title }}</div>
          <div class="sub">{{ panel.sub }}</div>
          <el-button class="close" @click="panelVisible = false">×</el-button>
        </div>
        <div class="panel-bd">
          <div class="kv" v-for="(r, i) in attrs" :key="i">
            <div class="k">{{ r.name ?? r.code }}</div>
            <div class="v">{{ r.value }}</div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, reactive, watch, nextTick } from "vue";
import * as d3 from "d3";
import { Aim, Connection, Rank, PieChart, FullScreen } from "@element-plus/icons-vue";

/* -------------------- Props & emits -------------------- */
const props = defineProps({
  data: { type: Object, default: () => ({}) },
  attrs: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(["update:panel", "load-children"]);

/* -------------------- State -------------------- */
const containerEl = ref(null);
let svg = null;
let simulation = null;
const isDragging = ref(false);
const currentLayoutKey = ref("radial"); // 'radial' | 'treeTB' | 'treeLR'
const lastSig = ref(null);

const panelVisible = defineModel("panelVisible", { type: Boolean, default: false });
const panel = reactive({
  title: "",
  sub: "",
});
const isActive = (k) => currentLayoutKey.value === k;

// === 全屏相关 ===
const wrapEl = ref(null);
const isFullscreen = ref(false);

async function enterFullscreen() {
  const el = wrapEl.value;
  if (!el) return;
  if (el.requestFullscreen) await el.requestFullscreen();
  else if (el.webkitRequestFullscreen) el.webkitRequestFullscreen();
  else if (el.msRequestFullscreen) el.msRequestFullscreen();
}

async function exitFullscreen() {
  if (document.exitFullscreen) await document.exitFullscreen();
  else if (document.webkitExitFullscreen) document.webkitExitFullscreen();
  else if (document.msExitFullscreen) document.msExitFullscreen();
}

async function toggleFullscreen() {
  if (isFullscreen.value) await exitFullscreen();
  else await enterFullscreen();
}

function onFsChange() {
  const fsEl = document.fullscreenElement || document.webkitFullscreenElement || document.msFullscreenElement;
  isFullscreen.value = !!fsEl;

  if (svg) {
    requestAnimationFrame(() => {
      try {
        updateSvgSize();
        handleToCenter();
      } catch {}
    });
  }
}

/* -------------------- UI/交互参数 -------------------- */
const UI = {
  nodeSize: 46,
  fontSize: 12,
  labelMaxWidth: 160,
};

// 颜色映射
const COLOR_BY_TYPE = {
  unit: { fill: "#FFF380", stroke: "#FFC107" },
  person: { fill: "#80D8FF", stroke: "#03A9F4" },
  train: { fill: "#A2F78D", stroke: "#4CAF50" },
  other: { fill: "#FFCDD2", stroke: "#F44336" },
};

const PALETTE = [
  { fill: "#FFF380", stroke: "#FFC107" },
  { fill: "#80D8FF", stroke: "#03A9F4" },
  { fill: "#A2F78D", stroke: "#4CAF50" },
  { fill: "#FFD1DC", stroke: "#E91E63" },
  { fill: "#FFE0B2", stroke: "#FF9800" },
  { fill: "#E1BEE7", stroke: "#9C27B0" },
  { fill: "#B2EBF2", stroke: "#00BCD4" },
  { fill: "#C8E6C9", stroke: "#8BC34A" },
  { fill: "#D1C4E9", stroke: "#673AB7" },
  { fill: "#FFECB3", stroke: "#FFC107" },
];

function computeColorAuto(std) {
  const types = Array.from(new Set((std.nodes || []).map((n) => (n.data && n.data.type) || "other")));
  const map = {};
  let i = 0;
  for (const t of types) map[t] = COLOR_BY_TYPE[t] || PALETTE[i++ % PALETTE.length];
  return map;
}
let colorMap = {};

/* -------------------- 数据处理 -------------------- */
function normalizeGraph(raw) {
  const nodes = raw?.nodes || [];
  const links = raw?.links || [];
  const stdNodes = [];
  const stdEdges = [];
  const idSet = new Set();

  for (const n of nodes) {
    const id = String(n.id);
    if (!id || idSet.has(id)) continue;
    idSet.add(id);
    const label =
      n.nodeName || (n.attrs?.find((a) => a.code === "name" || a.name === "name")?.value ?? "") || n.entityId || id;
    const type = (n?.labels && n.labels[0]) || n?.label || "other";
    stdNodes.push({
      id,
      label,
      type,
      raw: n,
      x: Math.random() * 800,
      y: Math.random() * 600,
      hidden: false,
      collapsed: false,
    });
  }

  let idx = 0;
  for (const e of links) {
    const s = String(e.source);
    const t = String(e.target);
    if (!idSet.has(s) || !idSet.has(t)) continue;
    stdEdges.push({
      id: e.id ? String(e.id) : `e_${idx++}`,
      source: s,
      target: t,
      label: e.name || "",
      relation: e.name || "",
      raw: e,
      hidden: false,
    });
  }
  return { nodes: stdNodes, edges: stdEdges };
}

/* -------------------- D3 图形渲染 -------------------- */
let graphData = { nodes: [], edges: [] };

function initSvg() {
  if (!containerEl.value) return;

  const container = d3.select(containerEl.value);
  container.selectAll("*").remove();

  svg = container.append("svg").attr("width", "100%").attr("height", "100%");

  // 添加缩放和拖拽行为
  const zoom = d3
    .zoom()
    .scaleExtent([0.1, 10])
    .on("zoom", (event) => {
      svg.select(".graph-group").attr("transform", event.transform);
    });

  svg.call(zoom);

  // 创建主绘图组
  const graphGroup = svg.append("g").attr("class", "graph-group");

  // 创建边组和节点组
  graphGroup.append("g").attr("class", "edges");
  graphGroup.append("g").attr("class", "nodes");

  // 点击空白区域取消选择
  svg.on("click", (event) => {
    if (event.target === svg.node()) {
      clearHighlight();
      panelVisible.value = false;
    }
  });
}

function updateSvgSize() {
  if (!svg || !containerEl.value) return;
  const rect = containerEl.value.getBoundingClientRect();
  svg.attr("width", rect.width).attr("height", rect.height);
}

function renderGraph() {
  if (!svg || !graphData) return;

  const visibleNodes = graphData.nodes.filter((d) => !d.hidden);
  const visibleEdges = graphData.edges.filter((d) => !d.hidden);

  // 渲染边
  const edgeSelection = svg
    .select(".edges")
    .selectAll(".edge")
    .data(visibleEdges, (d) => d.id);

  edgeSelection.exit().remove();

  const edgeEnter = edgeSelection.enter().append("g").attr("class", "edge");

  edgeEnter.append("line").attr("stroke", "#9aa0a6").attr("stroke-width", 1.4).attr("marker-end", "url(#arrowhead)");

  edgeEnter
    .append("text")
    .attr("class", "edge-label")
    .attr("text-anchor", "middle")
    .attr("font-size", 11)
    .attr("fill", "#666")
    .text((d) => d.label);

  const edgeUpdate = edgeEnter.merge(edgeSelection);

  // 渲染节点
  const nodeSelection = svg
    .select(".nodes")
    .selectAll(".node")
    .data(visibleNodes, (d) => d.id);

  nodeSelection.exit().remove();

  const nodeEnter = nodeSelection.enter().append("g").attr("class", "node").style("cursor", "pointer");

  nodeEnter
    .append("circle")
    .attr("r", UI.nodeSize / 2)
    .attr("stroke-width", 1.8);

  nodeEnter
    .append("text")
    .attr("text-anchor", "middle")
    .attr("dy", "0.35em")
    .attr("font-size", UI.fontSize)
    .attr("fill", "#262626")
    .style("pointer-events", "none")
    .text((d) => d.label);

  const nodeUpdate = nodeEnter.merge(nodeSelection);

  // 更新节点样式
  nodeUpdate
    .select("circle")
    .attr("fill", (d) => (colorMap[d.type] || COLOR_BY_TYPE.other).fill)
    .attr("stroke", (d) => (colorMap[d.type] || COLOR_BY_TYPE.other).stroke);

  // 添加节点交互
  nodeUpdate
    .on("click", handleNodeClick)
    .on("dblclick", handleNodeDblClick)
    .on("contextmenu", handleNodeRightClick)
    .call(d3.drag().on("start", dragStarted).on("drag", dragged).on("end", dragEnded));

  // 更新位置
  updatePositions(nodeUpdate, edgeUpdate);
}

function updatePositions(nodeSelection, edgeSelection) {
  if (nodeSelection) {
    nodeSelection.attr("transform", (d) => `translate(${d.x},${d.y})`);
  }

  if (edgeSelection) {
    edgeSelection
      .select("line")
      .attr("x1", (d) => {
        const source = graphData.nodes.find((n) => n.id === d.source);
        return source ? source.x : 0;
      })
      .attr("y1", (d) => {
        const source = graphData.nodes.find((n) => n.id === d.source);
        return source ? source.y : 0;
      })
      .attr("x2", (d) => {
        const target = graphData.nodes.find((n) => n.id === d.target);
        return target ? target.x : 0;
      })
      .attr("y2", (d) => {
        const target = graphData.nodes.find((n) => n.id === d.target);
        return target ? target.y : 0;
      });

    edgeSelection
      .select(".edge-label")
      .attr("x", (d) => {
        const source = graphData.nodes.find((n) => n.id === d.source);
        const target = graphData.nodes.find((n) => n.id === d.target);
        return source && target ? (source.x + target.x) / 2 : 0;
      })
      .attr("y", (d) => {
        const source = graphData.nodes.find((n) => n.id === d.source);
        const target = graphData.nodes.find((n) => n.id === d.target);
        return source && target ? (source.y + target.y) / 2 : 0;
      });
  }
}

// 添加箭头标记
function addArrowMarker() {
  if (!svg) return;

  svg
    .append("defs")
    .append("marker")
    .attr("id", "arrowhead")
    .attr("viewBox", "0 -5 10 10")
    .attr("refX", 8)
    .attr("refY", 0)
    .attr("markerWidth", 6)
    .attr("markerHeight", 6)
    .attr("orient", "auto")
    .append("path")
    .attr("d", "M0,-5L10,0L0,5")
    .attr("fill", "#9aa0a6");
}

/* -------------------- 交互处理 -------------------- */
let lastClickTime = 0;
let lastClickNode = null;

function handleNodeClick(event, d) {
  const now = Date.now();
  const isDoubleClick = now - lastClickTime < 300 && lastClickNode === d.id;

  if (isDoubleClick) {
    handleNodeDblClick(event, d);
    return;
  }

  lastClickTime = now;
  lastClickNode = d.id;

  // 高亮节点及其邻居
  highlightNode(d);
}

function handleNodeDblClick(event, d) {
  event.stopPropagation();

  // 检查是否有子节点
  const hasChildren = graphData.edges.some((e) => e.source === d.id);

  if (hasChildren) {
    // 切换折叠/展开
    toggleNodeChildren(d);
  } else {
    // 加载子节点
    emit("load-children", { id: d.id, raw: d.raw || {} });
  }
}

function handleNodeRightClick(event, d) {
  event.preventDefault();

  panel.title = d.label || d.id;
  panel.sub = d.type || "";
  emit("update:panel", d.raw || {});
  panelVisible.value = true;
}

function highlightNode(node) {
  // 清除之前的高亮
  clearHighlight();

  // 找到相邻节点和边
  const connectedEdges = graphData.edges.filter((e) => e.source === node.id || e.target === node.id);
  const connectedNodeIds = new Set();
  connectedEdges.forEach((e) => {
    connectedNodeIds.add(e.source);
    connectedNodeIds.add(e.target);
  });

  // 高亮相关元素
  svg
    .selectAll(".node")
    .classed("faded", (d) => !connectedNodeIds.has(d.id))
    .classed("highlight", (d) => d.id === node.id);

  svg
    .selectAll(".edge")
    .classed("faded", (d) => !connectedEdges.includes(d))
    .classed("highlight", (d) => connectedEdges.includes(d));
}

function clearHighlight() {
  svg.selectAll(".node, .edge").classed("faded", false).classed("highlight", false);
}

function toggleNodeChildren(node) {
  const childEdges = graphData.edges.filter((e) => e.source === node.id);
  const childNodeIds = childEdges.map((e) => e.target);
  const childNodes = graphData.nodes.filter((n) => childNodeIds.includes(n.id));

  const isCollapsed = node.collapsed;

  // 切换隐藏状态
  childNodes.forEach((n) => (n.hidden = !isCollapsed));
  childEdges.forEach((e) => (e.hidden = !isCollapsed));
  node.collapsed = !isCollapsed;

  // 重新渲染
  renderGraph();
  runLayout(currentLayoutKey.value);
}

/* -------------------- 拖拽处理 -------------------- */
function dragStarted(event, d) {
  if (!event.active && simulation) simulation.alphaTarget(0.3).restart();
  d.fx = d.x;
  d.fy = d.y;
  isDragging.value = true;
}

function dragged(event, d) {
  d.fx = event.x;
  d.fy = event.y;
}

function dragEnded(event, d) {
  if (!event.active && simulation) simulation.alphaTarget(0);
  d.fx = null;
  d.fy = null;
  isDragging.value = false;
}

/* -------------------- 布局算法 -------------------- */
function runLayout(mode = "radial") {
  if (!graphData.nodes.length) return;

  const visibleNodes = graphData.nodes.filter((d) => !d.hidden);
  const visibleEdges = graphData.edges.filter((d) => !d.hidden);

  if (simulation) {
    simulation.stop();
  }

  switch (mode) {
    case "radial":
      applyRadialLayout(visibleNodes, visibleEdges);
      break;
    case "treeTB":
      applyTreeLayout(visibleNodes, visibleEdges, "TB");
      break;
    case "treeLR":
      applyTreeLayout(visibleNodes, visibleEdges, "LR");
      break;
    default:
      applyForceLayout(visibleNodes, visibleEdges);
  }

  renderGraph();
}

function applyRadialLayout(nodes, edges) {
  if (!containerEl.value) return;

  const { width, height } = containerEl.value.getBoundingClientRect();
  const centerX = width / 2;
  const centerY = height / 2;

  // 计算层级
  const levels = computeNodeLevels(nodes, edges);
  const maxLevel = Math.max(...Object.values(levels));

  // 按层级分组
  const nodesByLevel = {};
  nodes.forEach((node) => {
    const level = levels[node.id] || 0;
    if (!nodesByLevel[level]) nodesByLevel[level] = [];
    nodesByLevel[level].push(node);
  });

  // 同心圆布局
  Object.keys(nodesByLevel).forEach((level) => {
    const levelNodes = nodesByLevel[level];
    const radius = level == 0 ? 0 : 80 + level * 100;
    const angleStep = (2 * Math.PI) / levelNodes.length;

    levelNodes.forEach((node, index) => {
      if (level == 0) {
        node.x = centerX;
        node.y = centerY;
      } else {
        const angle = index * angleStep;
        node.x = centerX + radius * Math.cos(angle);
        node.y = centerY + radius * Math.sin(angle);
      }
    });
  });
}

function applyTreeLayout(nodes, edges, direction = "TB") {
  if (!containerEl.value) return;

  const { width, height } = containerEl.value.getBoundingClientRect();

  // 创建层次结构
  const hierarchy = d3
    .stratify()
    .id((d) => d.id)
    .parentId((d) => {
      const parentEdge = edges.find((e) => e.target === d.id);
      return parentEdge ? parentEdge.source : null;
    })(nodes);

  const treeLayout = d3.tree().size(direction === "TB" ? [width - 100, height - 100] : [height - 100, width - 100]);

  const root = treeLayout(hierarchy);

  root.descendants().forEach((d) => {
    const node = nodes.find((n) => n.id === d.id);
    if (node) {
      if (direction === "TB") {
        node.x = d.x + 50;
        node.y = d.y + 50;
      } else {
        node.x = d.y + 50;
        node.y = d.x + 50;
      }
    }
  });
}

function applyForceLayout(nodes, edges) {
  simulation = d3
    .forceSimulation(nodes)
    .force(
      "link",
      d3
        .forceLink(edges)
        .id((d) => d.id)
        .distance(100)
    )
    .force("charge", d3.forceManyBody().strength(-300))
    .force("center", d3.forceCenter(400, 300))
    .force("collision", d3.forceCollide().radius(UI.nodeSize / 2 + 5));

  simulation.on("tick", () => {
    updatePositions(svg.selectAll(".node"), svg.selectAll(".edge"));
  });
}

function computeNodeLevels(nodes, edges) {
  const levels = {};
  const visited = new Set();
  const queue = [];

  // 找根节点（没有入边的节点）
  const hasIncoming = new Set(edges.map((e) => e.target));
  const roots = nodes.filter((n) => !hasIncoming.has(n.id));

  // 如果没有明显的根节点，选择第一个
  if (roots.length === 0 && nodes.length > 0) {
    roots.push(nodes[0]);
  }

  // BFS计算层级
  roots.forEach((root) => {
    levels[root.id] = 0;
    queue.push(root.id);
    visited.add(root.id);
  });

  while (queue.length > 0) {
    const nodeId = queue.shift();
    const currentLevel = levels[nodeId];

    edges.forEach((edge) => {
      if (edge.source === nodeId && !visited.has(edge.target)) {
        levels[edge.target] = currentLevel + 1;
        queue.push(edge.target);
        visited.add(edge.target);
      }
    });
  }

  return levels;
}

/* -------------------- 工具栏功能 -------------------- */
function handleFab(type) {
  currentLayoutKey.value = type;
  runLayout(type);
}

function handleToCenter() {
  if (!svg || !containerEl.value) return;

  const { width, height } = containerEl.value.getBoundingClientRect();
  const bounds = svg.select(".graph-group").node().getBBox();

  if (bounds.width === 0 || bounds.height === 0) return;

  const fullWidth = width;
  const fullHeight = height;
  const widthScale = fullWidth / bounds.width;
  const heightScale = fullHeight / bounds.height;
  const scale = Math.min(widthScale, heightScale) * 0.8;

  const centerX = bounds.x + bounds.width / 2;
  const centerY = bounds.y + bounds.height / 2;
  const translateX = fullWidth / 2 - centerX * scale;
  const translateY = fullHeight / 2 - centerY * scale;

  svg
    .transition()
    .duration(750)
    .call(d3.zoom().transform, d3.zoomIdentity.translate(translateX, translateY).scale(scale));
}

/* -------------------- 数据更新 -------------------- */
function buildOrUpdateGraph(raw, rebuild = false) {
  const std = normalizeGraph(raw || {});
  colorMap = computeColorAuto(std);
  graphData = std;

  if (!svg || rebuild) {
    initSvg();
    addArrowMarker();
  }

  renderGraph();
  runLayout(currentLayoutKey.value);
}

function applyChildren(parentId, payload) {
  if (!graphData) return;

  const parentNode = graphData.nodes.find((n) => n.id === String(parentId));
  if (!parentNode) return;

  // 添加新节点
  for (const nd of payload?.nodes || []) {
    const id = String(nd.id);
    if (!id) continue;

    const existingNode = graphData.nodes.find((n) => n.id === id);
    if (!existingNode) {
      const label =
        nd.nodeName ||
        (nd.attrs?.find((a) => a.code === "name" || a.name === "name")?.value ?? "") ||
        nd.entityId ||
        id;
      const type = (nd?.labels && nd.labels[0]) || nd?.label || "other";

      graphData.nodes.push({
        id,
        label,
        type,
        raw: nd,
        x: parentNode.x + (Math.random() - 0.5) * 100,
        y: parentNode.y + (Math.random() - 0.5) * 100,
        hidden: false,
        collapsed: false,
      });
    }
  }

  // 添加新边
  for (const e of payload?.links || []) {
    const sid = String(e.source);
    const tid = String(e.target);
    const rel = e.name || "";

    const sourceExists = graphData.nodes.some((n) => n.id === sid);
    const targetExists = graphData.nodes.some((n) => n.id === tid);

    if (sourceExists && targetExists) {
      const existingEdge = graphData.edges.find(
        (edge) => edge.source === sid && edge.target === tid && edge.relation === rel
      );

      if (!existingEdge) {
        graphData.edges.push({
          id: e.id ? String(e.id) : `e_${sid}_${tid}_${rel}`,
          source: sid,
          target: tid,
          label: rel,
          relation: rel,
          raw: e,
          hidden: false,
        });
      }
    }
  }

  // 重新渲染和布局
  renderGraph();
  runLayout(currentLayoutKey.value);
}

function makeDataSignature(d) {
  const nodes = d?.nodes ?? [];
  const links = d?.links ?? [];
  let h = 0x811c9dc5;
  const mix = (s) => {
    for (let i = 0; i < s.length; i++) {
      h ^= s.charCodeAt(i);
      h = (h >>> 0) * 16777619;
    }
  };
  mix(String(nodes.length));
  for (const n of nodes) mix(`${n.id}|${n.updatedAt ?? n.version ?? ""}`);
  mix(String(links.length));
  for (const e of links) mix(`${e.source}->${e.target}|${e.name ?? ""}`);
  return h >>> 0;
}

/* -------------------- 生命周期 -------------------- */
onMounted(async () => {
  await nextTick();
  buildOrUpdateGraph(props.data || {}, true);
  lastSig.value = makeDataSignature(props.data || {});

  document.addEventListener("fullscreenchange", onFsChange);
  document.addEventListener("webkitfullscreenchange", onFsChange);
  document.addEventListener("MSFullscreenChange", onFsChange);

  // 监听窗口大小变化
  window.addEventListener("resize", handleResize);
});

onBeforeUnmount(() => {
  if (simulation) {
    simulation.stop();
  }

  document.removeEventListener("fullscreenchange", onFsChange);
  document.removeEventListener("webkitfullscreenchange", onFsChange);
  document.removeEventListener("MSFullscreenChange", onFsChange);
  window.removeEventListener("resize", handleResize);
});

function handleResize() {
  if (svg && containerEl.value) {
    updateSvgSize();
    runLayout(currentLayoutKey.value);
  }
}

watch(
  () => props.data,
  async (raw) => {
    const sig = makeDataSignature(raw || {});
    if (sig === lastSig.value) return;
    lastSig.value = sig;
    buildOrUpdateGraph(raw || {}, false);
  },
  { deep: true, flush: "post" }
);

// 暴露给父组件
defineExpose({ applyChildren });
</script>

<style scoped lang="scss">
.wrap {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
}
.graph {
  width: 100%;
  height: 100%;
  cursor: grab;
}
.graph:active {
  cursor: grabbing;
}

/* 工具栏样式与原版相同 */
.tooldock {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 12;
}

.dock {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px;
  border-radius: 999px;
  background: rgba(255, 255, 255, 0.92);
  backdrop-filter: saturate(160%) blur(8px);
  border: 1px solid rgba(31, 35, 40, 0.08);
  box-shadow: 0 18px 40px rgba(0, 0, 0, 0.16), 0 2px 10px rgba(0, 0, 0, 0.06);
}

.dock-sep {
  width: 1px;
  height: 22px;
  background: linear-gradient(180deg, transparent, rgba(0, 0, 0, 0.12), transparent);
  margin: 0 2px;
}

.dock-btn {
  width: 20px;
  height: 20px;
  display: grid;
  place-items: center;
  border: 0;
  border-radius: 50%;
  background: #ecf5ff;
  color: #000;
  cursor: pointer;
  transition: transform 0.08s ease, box-shadow 0.18s ease, background-color 0.18s ease;
}

.dock-btn:hover {
  background: #e8f3ff;
  box-shadow: 0 4px 10px rgba(64, 158, 255, 0.25);
  transform: translateY(-1px);
}

.dock-btn:active {
  transform: translateY(0);
}

.dock-btn.active {
  background: #eef6ff;
  box-shadow: inset 0 0 0 2px #409eff;
}

:fullscreen .tooldock,
:-webkit-full-screen .tooldock {
  z-index: 100000;
}

@media (max-width: 640px) {
  .dock {
    max-width: calc(100vw - 24px);
    overflow-x: auto;
  }
}

/* 信息面板样式与原版相同 */
.info-panel {
  position: absolute;
  right: 12px;
  top: 64px;
  width: 340px;
  max-height: calc(100% - 96px);
  background: #111;
  color: #fff;
  border: 1px solid #333;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.35);
  z-index: 12;
}
.panel-hd {
  display: flex;
  align-items: start;
  gap: 8px;
  padding: 12px;
  border-bottom: 1px solid #222;
}
.title {
  font-weight: 700;
  font-size: 16px;
  flex: 1;
}
.sub {
  color: #aaa;
  font-size: 12px;
  margin-top: 2px;
}
.close {
  background: transparent;
  border: none;
  color: #aaa;
  font-size: 20px;
  cursor: pointer;
}
.panel-bd {
  padding: 10px 12px;
  max-height: 520px;
  overflow: auto;
}
.kv {
  display: grid;
  grid-template-columns: 110px 1fr;
  gap: 6px 10px;
  padding: 6px 0;
  border-bottom: 1px dashed #222;
}
.k {
  color: #409eff;
  word-break: break-all;
}
.v {
  color: #222;
  word-break: break-all;
}

/* D3 特有样式 */
:deep(.node) {
  cursor: pointer;
  transition: all 0.2s ease;
}

:deep(.node.highlight circle) {
  stroke-width: 3px;
  filter: brightness(1.1);
}

:deep(.node.faded) {
  opacity: 0.3;
}

:deep(.edge) {
  transition: all 0.2s ease;
}

:deep(.edge.highlight line) {
  stroke-width: 2.5px;
  stroke: #409eff;
}

:deep(.edge.faded) {
  opacity: 0.2;
}

:deep(.edge-label) {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  pointer-events: none;
  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

:deep(.node text) {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-weight: 500;
  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

:deep(.node:hover circle) {
  stroke-width: 2.5px;
  filter: brightness(1.05);
}

:deep(.node:hover text) {
  font-weight: 600;
}
</style>
